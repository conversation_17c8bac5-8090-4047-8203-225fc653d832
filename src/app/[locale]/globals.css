@tailwind base;
@tailwind components;
@tailwind utilities;

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
}

.navbar.show {
  transform: translateY(0);
}

.navbar.hide {
  transform: translateY(-100%);
}

.content {
  padding-top: 60px;
}

.text-gold-gradient {
  font-weight: bold;
  background: linear-gradient(to right, #F9C924, #E4AF18, #FFF98C, #FFD440);
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.button-gold-gradient {
  font-size: 16px;
  color: black;
  padding: 8px 18px 8px 18px;
  border-radius: 6px;
  background: linear-gradient(to right, #F9C924, #E4AF18, #FFF98C, #FFD440);
}

.border-gold-gradient {
  position: relative;
  background: linear-gradient(to right, #F9C924, #E4AF18, #FFF98C, #FFD440);
  padding: 1px;
  border-radius: 8px;
}