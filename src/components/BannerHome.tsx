"use client";
import Image from "next/image";
import mainBackground from "../../public/main_background.png";
import imagePresenter from "../../public/image_presenter3.png";

function BannerHome() {
  return (
    <>
      <style>
        {`
          @keyframes goldShimmer {
            0% {
              background-position: -200% 0;
            }
            100% {
              background-position: 200% 0;
            }
          }
        `}
      </style>
      <div className="h-screen relative">
        {/* Layer 1: รูปภาพพื้นหลัง */}
        <Image
          className="brightness-50 object-cover h-screen w-full absolute inset-0 z-0"
          alt="main_background"
          src={mainBackground}
          priority
          fill
        />

        {/* Layer 2: Gradient ซ้ายเข้ม */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent z-10"></div>

        {/* Layer 3: ตัวหนังสือและเนื้อหา */}
        <div className="relative flex h-full z-40">
          <div className="flex items-start justify-center w-full pt-24 sm:items-center sm:justify-start sm:mx-8 sm:pt-0">
            <div className="text-center sm:text-left">
              <p
                className="text-2xl sm:text-6xl py-4 font-bold"
                style={{
                  background: `linear-gradient(
                    90deg,
                    #ffe55c 0%,
                    #ffd700 15%,
                    #ffec8c 25%,
                    #fffacd 35%,
                    #ffd700 45%,
                    #daa520 55%,
                    #ffd700 65%,
                    #fffacd 75%,
                    #ffec8c 85%,
                    #ffd700 95%,
                    #ffe55c 100%
                  )`,
                  backgroundSize: "200% 100%",
                  animation: "goldShimmer 20s ease-in-out infinite",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow:
                    "1px 1px 0px rgba(0,0,0,0.3), 0px 0px 8px rgba(255,215,0,0.4)",
                }}
              >
                GruChangThai Antique
              </p>
              <p
                className="text-2xl sm:text-6xl py-4 font-bold"
                style={{
                  background: `linear-gradient(
                    90deg,
                    #ffe55c 0%,
                    #ffd700 15%,
                    #ffec8c 25%,
                    #fffacd 35%,
                    #ffd700 45%,
                    #daa520 55%,
                    #ffd700 65%,
                    #fffacd 75%,
                    #ffec8c 85%,
                    #ffd700 95%,
                    #ffe55c 100%
                  )`,
                  backgroundSize: "200% 100%",
                  animation: "goldShimmer 20s ease-in-out infinite",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow:
                    "1px 1px 0px rgba(0,0,0,0.3), 0px 0px 8px rgba(255,215,0,0.4)",
                  animationDelay: "0.5s",
                }}
              >
                Gold Jewelry
              </p>
              <p
                className="text-white mt-4 text-xl"
                style={{ textShadow: "1px 1px 0px rgba(0,0,0,0.6)" }}
              >
                The Perfect Jewels for you
              </p>
              <button
                className="text-xl mt-8 px-6 py-3 rounded-lg font-semibold hover:scale-105 transition-all duration-300"
                style={{
                  background:
                    "linear-gradient(135deg, #FFE55C 0%, #FFD700 25%, #DAA520 50%, #FFD700 75%, #FFEC8C 100%)",
                  color: "#8B4513",
                  textShadow: "1px 1px 0px rgba(255,255,255,0.4)",
                  boxShadow: "0 4px 15px rgba(255, 215, 0, 0.4)",
                }}
              >
                Explore More
              </button>
              <p
                className="text-white mt-8 text-xl"
                style={{ textShadow: "1px 1px 0px rgba(0,0,0,0.6)" }}
              >
                Call: 084-8047253
              </p>
            </div>
          </div>
        </div>

        {/* Layer 4: รูปภาพ presenter */}
        <Image
          className="object-contain w-full h-auto absolute bottom-0 left-0 z-30 sm:h-auto sm:max-h-full sm:w-auto sm:right-8 sm:bottom-0 sm:left-auto sm:top-auto"
          alt="image_presenter"
          src={imagePresenter}
        />
      </div>
    </>
  );
}
export default BannerHome;
