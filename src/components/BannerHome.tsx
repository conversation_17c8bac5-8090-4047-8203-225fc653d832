"use client";
import Image from "next/image";

function BannerHome() {
  return (
    <>
      <style>
        {`
          @keyframes goldShimmer {
            0% {
              background-position: -200% 0;
              text-shadow:
                0 0 5px rgba(255, 255, 255, 0.8),
                0 0 10px rgba(255, 255, 255, 0.6),
                0 0 15px rgba(255, 255, 255, 0.4),
                0 0 20px rgba(255, 215, 0, 0.8),
                0 0 35px rgba(255, 215, 0, 0.6),
                0 0 40px rgba(255, 215, 0, 0.4);
            }
            50% {
              background-position: 0% 0;
              text-shadow:
                0 0 2px rgba(255, 255, 255, 1),
                0 0 8px rgba(255, 255, 255, 0.8),
                0 0 12px rgba(255, 255, 255, 0.6),
                0 0 16px rgba(255, 215, 0, 1),
                0 0 25px rgba(255, 215, 0, 0.8),
                0 0 30px rgba(255, 215, 0, 0.6);
            }
            100% {
              background-position: 200% 0;
              text-shadow:
                0 0 5px rgba(255, 255, 255, 0.8),
                0 0 10px rgba(255, 255, 255, 0.6),
                0 0 15px rgba(255, 255, 255, 0.4),
                0 0 20px rgba(255, 215, 0, 0.8),
                0 0 35px rgba(255, 215, 0, 0.6),
                0 0 40px rgba(255, 215, 0, 0.4);
            }
          }

          @keyframes borderGlow {
            0% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
            50% {
              box-shadow:
                0 0 10px rgba(255, 255, 255, 0.8),
                0 0 20px rgba(255, 255, 255, 0.6),
                0 0 30px rgba(255, 215, 0, 0.8),
                inset 0 0 10px rgba(255, 255, 255, 0.4);
            }
            100% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
          }
        `}
      </style>

      <div className="h-screen relative overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`
          }}
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />

        {/* Content Container */}
        <div className="relative h-full flex items-center justify-between px-4 sm:px-8 lg:px-16 z-10">

          {/* Left Content */}
          <div className="flex-1 max-w-2xl">
            {/* Category Label */}
            <div className="mb-6">
              <span className="text-sm sm:text-base text-white/80 font-medium tracking-wider uppercase">
                LUXURY NECKLACE
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="mb-8">
              <div
                className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2"
                style={{
                  background: `linear-gradient(
                    45deg,
                    #FFE55C 0%,
                    #FFD700 10%,
                    #FFEC8C 20%,
                    #FFFACD 30%,
                    #FFD700 40%,
                    #DAA520 50%,
                    #FFD700 60%,
                    #FFFACD 70%,
                    #FFEC8C 80%,
                    #FFD700 90%,
                    #FFE55C 100%
                  )`,
                  backgroundSize: "300% 300%",
                  animation: "goldShimmer 3s ease-in-out infinite",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Graceful &
              </div>
              <div
                className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight"
                style={{
                  background: `linear-gradient(
                    45deg,
                    #FFE55C 0%,
                    #FFD700 10%,
                    #FFEC8C 20%,
                    #FFFACD 30%,
                    #FFD700 40%,
                    #DAA520 50%,
                    #FFD700 60%,
                    #FFFACD 70%,
                    #FFEC8C 80%,
                    #FFD700 90%,
                    #FFE55C 100%
                  )`,
                  backgroundSize: "300% 300%",
                  animation: "goldShimmer 3s ease-in-out infinite",
                  animationDelay: "0.5s",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Eye-Catching
              </div>
              <div
                className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight"
                style={{
                  background: `linear-gradient(
                    45deg,
                    #FFE55C 0%,
                    #FFD700 10%,
                    #FFEC8C 20%,
                    #FFFACD 30%,
                    #FFD700 40%,
                    #DAA520 50%,
                    #FFD700 60%,
                    #FFFACD 70%,
                    #FFEC8C 80%,
                    #FFD700 90%,
                    #FFE55C 100%
                  )`,
                  backgroundSize: "300% 300%",
                  animation: "goldShimmer 3s ease-in-out infinite",
                  animationDelay: "1s",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Necklaces
              </div>
            </h1>

            {/* Description */}
            <p className="text-white/90 text-lg sm:text-xl mb-8 leading-relaxed">
              Get lifted with the 21 Day Facial Gua Sha<br />
              Challenge!
            </p>

            {/* CTA Button */}
            <button
              className="px-8 py-4 text-lg font-semibold text-amber-900 rounded-lg hover:scale-105 transition-all duration-300 border-2 border-transparent"
              style={{
                background: "linear-gradient(135deg, #FFE55C 0%, #FFD700 25%, #DAA520 50%, #FFD700 75%, #FFEC8C 100%)",
                animation: "borderGlow 2s ease-in-out infinite",
              }}
            >
              Shop Now
            </button>
          </div>

          {/* Right Content - Model Image (Hidden on mobile) */}
          <div className="hidden lg:block flex-1 max-w-lg">
            <div className="relative h-full flex items-end justify-end">
              <Image
                src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Luxury Necklace Model"
                width={500}
                height={700}
                className="object-cover rounded-lg"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default BannerHome;
