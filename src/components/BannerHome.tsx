"use client";
import Image from "next/image";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/autoplay';

function BannerHome() {
  const images = [
    "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1606760227091-3dd870d97f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  ];

  return (
    <>
      <style>
        {`
          @keyframes borderGlow {
            0% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
            50% {
              box-shadow:
                0 0 10px rgba(255, 255, 255, 0.8),
                0 0 20px rgba(255, 255, 255, 0.6),
                0 0 30px rgba(255, 215, 0, 0.8),
                inset 0 0 10px rgba(255, 255, 255, 0.4);
            }
            100% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
          }

          .swiper-slide {
            background-size: 100% 100% !important;
            background-position: center !important;
            background-repeat: no-repeat !important;
            width: 100% !important;
            height: 100% !important;
          }

          .swiper-wrapper {
            width: 100% !important;
            height: 100% !important;
          }

          .swiper {
            width: 100% !important;
            height: 100% !important;
          }
        `}
      </style>

      <div className="h-screen relative overflow-hidden">
        {/* Background Swiper */}
        <Swiper
          modules={[Autoplay]}
          slidesPerView={1}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
            // reverseDirection: true,
          }}
          speed={1000}
          loop={true}
          className="absolute inset-0 w-full h-full"
          style={{ zIndex: 1 }}
        >
          {images.map((image, index) => (
            <SwiperSlide key={index} className="relative">
              <Image
                src={image}
                alt={`Slide ${index + 1}`}
                แน
                className="object-fill"
                style={{
                  objectFit: 'fill',
                  objectPosition: 'center',
                  transform: 'scale(1.05)',
                }}
                priority={index === 0}
                sizes="100vw"
              />
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent z-20" />

        {/* Content Container */}
        <div className="relative h-full flex items-center justify-between px-4 sm:px-8 lg:px-16 z-30">

          {/* Left Content */}
          <div className="flex-1 max-w-2xl">
            {/* Category Label */}
            <div className="mb-6">
              <span className="text-sm sm:text-base text-white/80 font-medium tracking-wider uppercase">
                LUXURY NECKLACE
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="mb-8">
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Graceful &
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Eye-Catching
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight text-white">
                Necklaces
              </div>
            </h1>

            {/* Description */}
            <p className="text-white/90 text-lg sm:text-xl mb-8 leading-relaxed">
              Get lifted with the 21 Day Facial Gua Sha<br />
              Challenge!
            </p>

            {/* CTA Button */}
            <button
              className="px-8 py-4 text-lg font-semibold text-amber-900 rounded-lg hover:scale-105 transition-all duration-300 border-2 border-transparent"
              style={{
                background: "linear-gradient(135deg, #FFE55C 0%, #FFD700 25%, #DAA520 50%, #FFD700 75%, #FFEC8C 100%)",
                animation: "borderGlow 2s ease-in-out infinite",
              }}
            >
              Shop Now
            </button>
          </div>

          {/* Right Content - Model Image (Hidden on mobile) */}
          <div className="hidden lg:block flex-1 max-w-lg">
            <div className="relative h-full flex items-end justify-end">
              <Image
                src={images[1]}
                alt="Luxury Necklace Model"
                width={500}
                height={700}
                className="object-cover rounded-lg"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default BannerHome;
