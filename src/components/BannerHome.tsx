"use client";
import Image from "next/image";
import { useState, useEffect, useMemo } from "react";

function BannerHome() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState(false);

  const images = useMemo(() => [
    "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1573408301185-9146fe634ad0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1606760227091-3dd870d97f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  ], []);

  // Preload images
  useEffect(() => {
    let loadedCount = 0;
    const totalImages = images.length;

    const checkAllLoaded = () => {
      loadedCount++;
      if (loadedCount >= totalImages) {
        setImagesLoaded(true);
      }
    };

    // Create img elements and start loading
    images.forEach((src) => {
      const img = document.createElement('img');
      img.onload = checkAllLoaded;
      img.onerror = checkAllLoaded; // Count failed loads too
      img.src = src;
    });

    // Fallback timeout - show images after 3 seconds regardless
    const timeout = setTimeout(() => {
      setImagesLoaded(true);
    }, 3000);

    return () => clearTimeout(timeout);
  }, [images]);

  useEffect(() => {
    setIsClient(true);

    // Only start slideshow after images are loaded
    if (!imagesLoaded) return;

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images.length, imagesLoaded]);

  if (!isClient) {
    return (
      <div className="h-screen relative overflow-hidden">
        <div className="absolute inset-0 bg-gray-800" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
        <div className="relative h-full flex items-center justify-between px-4 sm:px-8 lg:px-16 z-10">
          <div className="flex-1 max-w-2xl">
            <div className="mb-6">
              <span className="text-sm sm:text-base text-white/80 font-medium tracking-wider uppercase">
                LUXURY NECKLACE
              </span>
            </div>
            <h1 className="mb-8">
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Graceful &
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Eye-Catching
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight text-white">
                Necklaces
              </div>
            </h1>
            <p className="text-white/90 text-lg sm:text-xl mb-8 leading-relaxed">
              Get lifted with the 21 Day Facial Gua Sha<br />
              Challenge!
            </p>
            <button className="px-8 py-4 text-lg font-semibold text-amber-900 rounded-lg hover:scale-105 transition-all duration-300 border-2 border-transparent bg-gradient-to-r from-yellow-400 to-yellow-600">
              Shop Now
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <style>
        {`
          @keyframes borderGlow {
            0% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
            50% {
              box-shadow:
                0 0 10px rgba(255, 255, 255, 0.8),
                0 0 20px rgba(255, 255, 255, 0.6),
                0 0 30px rgba(255, 215, 0, 0.8),
                inset 0 0 10px rgba(255, 255, 255, 0.4);
            }
            100% {
              box-shadow:
                0 0 5px rgba(255, 255, 255, 0.5),
                0 0 10px rgba(255, 255, 255, 0.3),
                0 0 15px rgba(255, 215, 0, 0.5),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
            }
          }

          @keyframes zoomOut {
            0% {
              transform: scale(1.1);
            }
            100% {
              transform: scale(1);
            }
          }

          @keyframes slideIn {
            0% {
              transform: translateX(100%) scale(1.1);
              opacity: 0;
            }
            10% {
              transform: translateX(0%) scale(1.1);
              opacity: 1;
            }
            90% {
              transform: translateX(0%) scale(1);
              opacity: 1;
            }
            100% {
              transform: translateX(0%) scale(1);
              opacity: 1;
            }
          }
        `}
      </style>

      <div className="h-screen relative overflow-hidden">
        {/* Background Images - All loaded at once */}
        {images.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 bg-cover bg-center bg-no-repeat transition-all duration-1000 ease-in-out ${
              index === currentImageIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
            }`}
            style={{
              backgroundImage: `url('${image}')`,
              animation: index === currentImageIndex ? "slideIn 5s ease-out" : "none",
              transform: "scale(1.1)",
              willChange: 'transform, opacity',
            }}
          />
        ))}

        {/* Fallback background */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-amber-900 via-amber-800 to-amber-900 z-0"
          style={{
            opacity: imagesLoaded ? 0 : 1,
            transition: 'opacity 1s ease-in-out'
          }}
        />

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent z-20" />

        {/* Content Container */}
        <div className="relative h-full flex items-center justify-between px-4 sm:px-8 lg:px-16 z-30">

          {/* Left Content */}
          <div className="flex-1 max-w-2xl">
            {/* Category Label */}
            <div className="mb-6">
              <span className="text-sm sm:text-base text-white/80 font-medium tracking-wider uppercase">
                LUXURY NECKLACE
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="mb-8">
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Graceful &
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight mb-2 text-white">
                Eye-Catching
              </div>
              <div className="text-4xl sm:text-5xl lg:text-7xl font-bold leading-tight text-white">
                Necklaces
              </div>
            </h1>

            {/* Description */}
            <p className="text-white/90 text-lg sm:text-xl mb-8 leading-relaxed">
              Get lifted with the 21 Day Facial Gua Sha<br />
              Challenge!
            </p>

            {/* CTA Button */}
            <button
              className="px-8 py-4 text-lg font-semibold text-amber-900 rounded-lg hover:scale-105 transition-all duration-300 border-2 border-transparent"
              style={{
                background: "linear-gradient(135deg, #FFE55C 0%, #FFD700 25%, #DAA520 50%, #FFD700 75%, #FFEC8C 100%)",
                animation: "borderGlow 2s ease-in-out infinite",
              }}
            >
              Shop Now
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export default BannerHome;
